
<link rel="stylesheet" href="css/employee_attendance_mapping.css">
<div class="app-container" data-ng-init="init()" ng-controller="employeeAttendanceMappingCtrl">
<!--    <header class="header">-->
<!--        <div class="logo">-->
<!--            <i class="fa fa-code-branch"></i>-->
<!--            <h1>Employee Attendance Mapping</h1>-->
<!--        </div>-->
<!--    </header>-->
    <div class="container">
        <div class="header">
            <h1>Employee Eligibility Mapping</h1>
            <p>Configure attendance and approval eligibility mappings for employees</p>
        </div>

        <div class="step-indicator">
            <div class="step active" id="step1">
                <div class="step-number">1</div>
                <div class="step-label">Select Employees</div>
            </div>
            <div class="step-connector"></div>
            <div class="step" id="step2">
                <div class="step-number">2</div>
                <div class="step-label">Configure Mappings</div>
            </div>
            <div class="step-connector"></div>
            <div class="step" id="step3">
                <div class="step-number">3</div>
                <div class="step-label">Review & Save</div>
            </div>
        </div>

        <div class="main-content">
            <!-- Step 1: Employee Selection -->
            <div class="employee-selection" id="employeeSelection" ng-show="currentStep === 1">
                <div class="search-section">
                    <div class="search-title">🔍 Filter Employees</div>
                    <div class="filters-row">
                        <div class="form-group">
                            <label>Designation</label>
                            <select class="form-control" ng-model="filters.designation" ng-change="searchEmployees()">
                                <option value="All Designations">All Designations</option>
                                <option ng-repeat="designation in designations" value="{{designation.name || designation.designationName || designation}}">{{designation.name || designation.designationName || designation}}</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Department</label>
                            <select class="form-control" ng-model="filters.department" ng-change="searchEmployees()">
                                <option value="All Departments">All Departments</option>
                                <option ng-repeat="dept in departments" value="{{dept.name || dept.departmentName || dept}}">{{dept.name || dept.departmentName || dept}}</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button class="search-btn" dta-ng-click="searchEmployees()">🔍 Search</button>
                        </div>
                    </div>
                </div>

                <div class="employee-list">
                    <div class="list-header">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <div class="list-title">👥 Employee List</div>
                            <input type="text" class="form-control" placeholder="Search by name..." ng-model="searchText" ng-change="searchEmployees()" style="width: 200px; margin: 0;">
                        </div>
                        <div class="select-all" ng-click="toggleSelectAll()">
                            <input type="checkbox" ng-checked="selectedEmployees.length === paginatedEmployees.length && paginatedEmployees.length > 0"> Select All
                        </div>
                    </div>

                    <!-- Employee Count Info -->
                    <div class="employee-count-info">
                        <span class="count-badge">{{filteredEmployees.length}}</span> employees found
                        <span ng-show="filteredEmployees.length !== employees.length"> (filtered from {{employees.length}} total)</span>
                    </div>

                    <div class="employee-item" ng-repeat="employee in paginatedEmployees">
                        <input type="checkbox" class="employee-checkbox" ng-checked="isEmployeeSelected(employee)" ng-click="toggleEmployeeSelection(employee)">
                        <div class="employee-info">
                            <div class="employee-name">{{employee.name || employee.firstName + ' ' + employee.lastName}}</div>
                            <div class="employee-details">{{employee.employeeId || employee.id}} • {{employee.designation || employee.designationName || 'N/A'}} • {{employee.department || employee.departmentName || 'N/A'}}</div>
                        </div>

                    </div>

                    <div ng-if="filteredEmployees.length === 0" class="empty-state">
                        <div class="empty-icon">👥</div>
                        <div class="empty-text">No employees found</div>
                        <div class="empty-subtext">Try adjusting your search or filter criteria</div>
                    </div>

                    <!-- Pagination -->
                    <div class="pagination-container" ng-show="filteredEmployees.length > 0">
                        <div class="pagination-info">
                            Showing {{getStartIndex()}} to {{getEndIndex()}} of {{filteredEmployees.length}} employees
                        </div>
                        <div class="pagination-controls">
                            <button class="pagination-btn" ng-click="goToPage(1)" ng-disabled="currentPage === 1">«</button>
                            <button class="pagination-btn" ng-click="goToPage(currentPage - 1)" ng-disabled="currentPage === 1">‹</button>

                            <button class="pagination-btn" ng-repeat="page in getVisiblePages()"
                                    ng-click="goToPage(page)" ng-class="{active: page === currentPage}"
                                    ng-show="page !== '...'">
                                {{page}}
                            </button>
                            <span class="pagination-ellipsis" ng-repeat="page in getVisiblePages()" ng-show="page === '...'">...</span>

                            <button class="pagination-btn" ng-click="goToPage(currentPage + 1)" ng-disabled="currentPage === totalPages">›</button>
                            <button class="pagination-btn" ng-click="goToPage(totalPages)" ng-disabled="currentPage === totalPages">»</button>
                        </div>
                    </div>
                </div>

                <div class="selected-summary">
                    <div class="selected-count">Selected: {{selectedEmployees.length}} employees
                        <div class="employees-chips">
                        <span ng-repeat="employee in selectedEmployees" class="employee-chip">
                            {{employee.name || employee.firstName + ' ' + employee.lastName}} ({{employee.employeeId || employee.id}})
                        </span>
                            <div ng-if="selectedEmployees.length === 0" class="empty-chips">
                                No employees selected
                            </div>
                        </div>
                    </div>
                    <button class="next-btn" ng-click="goToMapping()">Next Step →</button>
                </div>
            </div>

            <!-- Step 2: Mapping Configuration -->
            <div class="mapping-section" id="mappingSection" ng-show="currentStep === 2">
                <div class="eligibility-tabs">
                    <button class="tab-btn active" ng-click="switchEligibilityTab('attendance')">
                        📅 Attendance Eligibility
                    </button>
                    <button class="tab-btn" ng-click="switchEligibilityTab('approval')">
                        ✅ Approval Eligibility
                    </button>
                </div>

                <div class="eligibility-content" id="attendanceTab">
                    <div class="mapping-tabs">
                        <button class="mapping-tab active" ng-click="switchMappingTab('unit')">Unit Mapping</button>
                        <button class="mapping-tab" ng-click="switchMappingTab('city')">City Mapping</button>
                        <button class="mapping-tab" ng-click="switchMappingTab('region')">Region Mapping</button>
                    </div>

                    <div class="mapping-form">
                        <div class="search-title">🏢 Configure Unit Mapping</div>

                        <!-- Unit Filters Section -->
                        <div class="unit-filters-section" ng-show="selectedMappingType === 'unit'">
                            <div class="unit-filters-title">
                                🔍 Filter Units
                                <button class="clear-filters-btn" ng-click="clearUnitFilters()" title="Clear all filters">Clear Filters</button>
                            </div>
                            <div class="filter-row">
                                <div class="form-group">
                                    <label>Category</label>
                                    <div class="searchable-dropdown-container">
                                        <input type="text" class="searchable-input" placeholder="Search categories..."
                                               ng-model="categorySearch" ng-focus="showCategoryDropdown = true"
                                               ng-blur="hideCategoryDropdown()" ng-keyup="filterCategories()">
                                        <div class="dropdown-options" ng-class="{show: showCategoryDropdown}">
                                            <div class="dropdown-option" ng-repeat="category in filteredCategories"
                                                 ng-click="selectCategory(category)" ng-mousedown="$event.preventDefault()">
                                                {{category.name}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>City</label>
                                    <div class="searchable-dropdown-container">
                                        <input type="text" class="searchable-input" placeholder="Search cities..."
                                               ng-model="citySearch" ng-focus="showCityDropdown = true"
                                               ng-blur="hideCityDropdown()" ng-keyup="filterCities()">
                                        <div class="dropdown-options" ng-class="{show: showCityDropdown}">
                                            <div class="dropdown-option" ng-repeat="city in filteredCities"
                                                 ng-click="selectCity(city)" ng-mousedown="$event.preventDefault()">
                                                {{city.name || city}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Region</label>
                                    <div class="searchable-dropdown-container">
                                        <input type="text" class="searchable-input" placeholder="Search regions..."
                                               ng-model="regionSearch" ng-focus="showRegionDropdown = true"
                                               ng-blur="hideRegionDropdown()" ng-keyup="filterRegions()">
                                        <div class="dropdown-options" ng-class="{show: showRegionDropdown}">
                                            <div class="dropdown-option" ng-repeat="region in filteredRegions"
                                                 ng-click="selectRegion(region)" ng-mousedown="$event.preventDefault()">
                                                {{region.name || region}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Unit Zones</label>
                                    <div class="searchable-dropdown-container">
                                        <input type="text" class="searchable-input" placeholder="Search Unit Zones..."
                                               ng-model="unitZoneSearch" ng-focus="showUnitZoneDropdown = true"
                                               ng-blur="hideUnitZoneDropdown()" ng-keyup="filterUnitZones()">
                                        <div class="dropdown-options" ng-class="{show: showUnitZoneDropdown}">
                                            <div class="dropdown-option" ng-repeat="unitZone in filteredUnitZones"
                                                 ng-click="selectUnitZone(unitZone)" ng-mousedown="$event.preventDefault()">
                                                {{unitZone.name}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="filter-info" ng-show="filteredUnits.length !== allUnits.length">
                                📊 Showing {{filteredUnits.length}} of {{allUnits.length}} units based on your filters
                            </div>
                        </div>

                        <!-- Unit Selection Row -->
                        <div class="unit-selection-row" ng-show="selectedMappingType === 'unit'">
                            <div class="form-group">
                                <label>Units <span style="color: #e74c3c;">*</span></label>
                                <div class="searchable-dropdown-container">
                                    <input type="text" class="searchable-input" placeholder="Search units..."
                                           ng-model="unitSearch" ng-focus="showUnitDropdown = true"
                                           ng-blur="hideUnitDropdown()" ng-keyup="filterUnitsDropdown()">
                                    <div class="dropdown-options" ng-class="{show: showUnitDropdown}">
                                        <div class="dropdown-option" ng-repeat="unit in filteredUnitsDropdown"
                                             ng-click="selectUnit(unit)" ng-mousedown="$event.preventDefault()">
                                            {{unit.id}} - {{unit.name}}
                                        </div>
                                    </div>
                                </div>
                                <div class="unit-info-display" ng-show="unitFilters.selectedUnitId">
                                    Selected: {{unitFilters.selectedUnitId}} - {{unitFilters.selectedUnitName}}
                                </div>
                            </div>
                        </div>

                        <!-- Non-unit mapping inputs -->
                        <div class="form-row" ng-show="selectedMappingType !== 'unit'">
                            <div class="form-group">
                                <div class="form-group">
                                    <label>City Name</label>
                                    <select class="form-control searchable" ng-model="unitFilters.city" ng-change="onUnitFilterChange()">
                                        <option value="">All Cities</option>
                                        <option ng-repeat="city in cities" value="{{city.name || city}}">{{city.name || city}}</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Region</label>
                                    <select class="form-control searchable" ng-model="unitFilters.region" ng-change="onUnitFilterChange()">
                                        <option value="">All Regions</option>
                                        <option ng-repeat="region in regions" value="{{region.name || region}}">{{region.name || region}}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Apply To Section -->
                        <div class="form-row">
                            <div class="form-group">
                                <label>Apply To</label>
                                <select class="form-control" ng-model="applyTo" id="applyToSelect" ng-change="toggleIndividualSelection()">
                                    <option value="all">All Selected Employees</option>
                                    <option value="individual">Individual Selection</option>
                                </select>
                            </div>
                        </div>

                        <!-- Individual Selection Panel -->
                        <div class="individual-selection-panel hidden" id="individualSelectionPanel">
                            <div class="search-title">👤 Select Individual Employees</div>
                            <div class="individual-employees">
                                <div ng-repeat="employee in selectedEmployees" class="individual-employee-item">
                                    <input type="checkbox" ng-model="employee.selectedForMapping">
                                    <span>{{employee.name || employee.firstName + ' ' + employee.lastName}} ({{employee.employeeId || employee.id}})</span>
                                </div>
                            </div>
                        </div>

                        <button class="add-mapping-btn" ng-click="addMapping()">➕ Add Mapping</button>
                    </div>

                    <div class="current-mappings">
                        <div class="mappings-header">
                            <div class="search-title">📋 Current Mappings</div>
                            <div class="mappings-controls">
                                <div class="mappings-count">{{mappings.length}} mappings</div>
                                <div class="mappings-actions">
                                    <input type="text" class="mapping-search" placeholder="🔍 Search mappings..." id="mappingSearch" ng-keyup="filterMappings()">
                                    <select class="mapping-filter" ng-model="mappingFilter" id="mappingFilter" ng-change="filterMappings()">
                                        <option value="all">All Types</option>
                                        <option value="unit">Unit Only</option>
                                        <option value="city">City Only</option>
                                        <option value="region">Region Only</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mappings-list">
                            <div class="mapping-item" ng-repeat="mapping in mappings" data-type="{{mapping.type}}" data-value="{{mapping.value.toLowerCase()}}" data-employees="{{getEmployeeNames(mapping.employees).toLowerCase()}}">
                                <div class="mapping-badge" ng-class="{'unit-badge': mapping.type === 'unit', 'city-badge': mapping.type === 'city', 'region-badge': mapping.type === 'region'}">{{mapping.type.toUpperCase()}}</div>
                                <div class="mapping-info">
                                    <div class="mapping-value">{{mapping.name || mapping.value}}</div>
                                    <div class="mapping-employees">
                                        <span class="employee-count">{{mapping.employees.length}} employee{{mapping.employees.length !== 1 ? 's' : ''}}:</span>
                                        <span class="employee-names">{{getEmployeeNames(mapping.employees)}}</span>
                                    </div>
                                </div>
                                <div class="mapping-actions-btn">
                                    <button class="edit-mapping" ng-click="editMapping($index)" title="Edit Mapping">✏️</button>
                                    <button class="remove-mapping" ng-click="removeMapping($index)" title="Remove Mapping">🗑️</button>
                                </div>
                            </div>
                        </div>

                        <div class="no-mappings" ng-show="mappings.length === 0">
                            <div class="empty-state">
                                <div class="empty-icon">📝</div>
                                <div class="empty-text">No mappings found</div>
                                <div class="empty-subtext">Add some mappings to get started</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="back-btn" ng-click="goBackToEmployeeSelection()">← Back<div class="bulk-actions">
                        <button class="bulk-btn">📤 Bulk Upload</button>
                        <button class="bulk-btn">📥 Download Template</button>
                        <button class="bulk-btn">📊 Export Current</button>
                    </div>

                    <button class="save-btn" ng-click="saveMappings()">💾 Save All Mappings</button>
                </div>
            </div>
        </div>
    </div>
</div>